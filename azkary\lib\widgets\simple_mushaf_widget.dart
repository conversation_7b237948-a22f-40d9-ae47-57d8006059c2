import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import '../services/mushaf_api_service.dart';

/// ويدجت مصحف مبسط يعرض البيانات الخام من API بدون تنسيقات إضافية
class SimpleMushafWidget extends StatelessWidget {
  final List<UthmaniAyah> ayahs;
  final int? highlightedAyahNumber;
  final Function(int ayahNumber)? onAyahTap;
  final String surahName;

  const SimpleMushafWidget({
    Key? key,
    required this.ayahs,
    required this.surahName,
    this.highlightedAyahNumber,
    this.onAyahTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        // خلفية بسيطة مناسبة للقراءة
        color: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFFFDF5),
      ),
      child: Column(
        children: [
          // رأس بسيط يعرض اسم السورة فقط
          _buildSimpleHeader(theme, isDarkMode),
          
          // محتوى المصحف - الآيات الخام من API
          Expanded(
            child: _buildAyahsList(theme, isDarkMode),
          ),
        ],
      ),
    );
  }

  /// بناء رأس بسيط
  Widget _buildSimpleHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: Text(
        surahName,
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.white : Colors.black87,
          fontFamily: 'Amiri',
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  /// بناء قائمة الآيات الخام من API
  Widget _buildAyahsList(ThemeData theme, bool isDarkMode) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: ayahs.length,
      itemBuilder: (context, index) {
        final ayah = ayahs[index];
        return _buildAyahItem(ayah, theme, isDarkMode);
      },
    );
  }

  /// بناء عنصر الآية الواحدة
  Widget _buildAyahItem(UthmaniAyah ayah, ThemeData theme, bool isDarkMode) {
    final isHighlighted = highlightedAyahNumber == ayah.numberInSurah;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isHighlighted
            ? (isDarkMode ? Colors.amber[700]!.withValues(alpha: 0.2) : Colors.brown[100])
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isHighlighted
            ? Border.all(
                color: isDarkMode ? Colors.amber[700]! : Colors.brown[400]!,
                width: 1,
              )
            : null,
      ),
      child: GestureDetector(
        onTap: () => onAyahTap?.call(ayah.numberInSurah),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // النص العثماني الخام من API
            RichText(
              textAlign: TextAlign.justify,
              textDirection: TextDirection.rtl,
              text: TextSpan(
                children: [
                  // النص العثماني الأصلي
                  TextSpan(
                    text: ayah.text,
                    style: TextStyle(
                      fontSize: 24,
                      height: 2.0,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black87,
                      fontFamily: 'Uthmani', // الخط العثماني الأصلي
                      letterSpacing: 0.5,
                    ),
                  ),
                  // رقم الآية بالأرقام العربية
                  TextSpan(
                    text: ' ﴿${_convertToArabicNumbers(ayah.numberInSurah)}﴾',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.amber[400] : Colors.brown[600],
                      fontFamily: 'Amiri',
                    ),
                  ),
                ],
              ),
            ),
            
            // معلومات إضافية من API (اختيارية)
            if (ayah.sajda) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.green[700] : Colors.green[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'سجدة',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.green[800],
                    fontFamily: 'Amiri',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
            
            // معلومات الصفحة والجزء من API
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الجزء ${_convertToArabicNumbers(ayah.juz)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    fontFamily: 'Amiri',
                  ),
                ),
                Text(
                  'الصفحة ${_convertToArabicNumbers(ayah.page)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    fontFamily: 'Amiri',
                  ),
                ),
                Text(
                  'الحزب ${_convertToArabicNumbers(ayah.hizbQuarter)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    fontFamily: 'Amiri',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
