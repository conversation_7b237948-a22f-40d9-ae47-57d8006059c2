import 'package:hijri/hijri.dart';
import 'package:adhan/adhan.dart';
import 'package:geolocator/geolocator.dart';
import '../models/islamic_calendar_model.dart';
import '../utils/logger.dart';

/// خدمة التقويم الإسلامي
class IslamicCalendarService {
  static Position? _lastKnownPosition;

  /// الحصول على بيانات التقويم لتاريخ معين
  static Future<IslamicCalendarModel> getCalendarData(DateTime date) async {
    try {
      // تحويل التاريخ الميلادي إلى هجري
      final hijriDate = HijriCalendar.fromDate(date);
      
      // الحصول على الأحداث الإسلامية
      final events = IslamicEventsFactory.getEventsForDate(hijriDate);
      
      // حساب مرحلة القمر
      final moonPhase = MoonPhaseCalculator.calculateMoonPhase(date);
      
      // الحصول على أوقات الصلاة
      final prayerTimes = await _getPrayerTimes(date);

      return IslamicCalendarModel(
        hijriDate: hijriDate,
        gregorianDate: date,
        events: events,
        moonPhase: moonPhase,
        prayerTimes: prayerTimes,
      );
    } catch (e) {
      AppLogger.error('خطأ في الحصول على بيانات التقويم: $e');
      rethrow;
    }
  }

  /// الحصول على بيانات الشهر الكامل
  static Future<List<IslamicCalendarModel>> getMonthData(int year, int month) async {
    try {
      final monthData = <IslamicCalendarModel>[];
      final firstDay = DateTime(year, month, 1);
      final lastDay = DateTime(year, month + 1, 0);

      for (int day = 1; day <= lastDay.day; day++) {
        final date = DateTime(year, month, day);
        final calendarData = await getCalendarData(date);
        monthData.add(calendarData);
      }

      return monthData;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على بيانات الشهر: $e');
      rethrow;
    }
  }

  /// الحصول على أوقات الصلاة
  static Future<PrayerTimes?> _getPrayerTimes(DateTime date) async {
    try {
      // الحصول على الموقع الجغرافي
      final position = await _getCurrentPosition();
      if (position == null) return null;

      // إعداد الإحداثيات
      final coordinates = Coordinates(position.latitude, position.longitude);
      
      // إعداد معاملات الحساب
      final params = CalculationMethod.muslimWorldLeague.getParameters();
      params.madhab = Madhab.shafi;

      // حساب أوقات الصلاة
      final prayerTimes = PrayerTimes.today(coordinates, params);

      return PrayerTimes(
        fajr: prayerTimes.fajr,
        sunrise: prayerTimes.sunrise,
        dhuhr: prayerTimes.dhuhr,
        asr: prayerTimes.asr,
        maghrib: prayerTimes.maghrib,
        isha: prayerTimes.isha,
      );
    } catch (e) {
      AppLogger.error('خطأ في حساب أوقات الصلاة: $e');
      return null;
    }
  }

  /// الحصول على الموقع الجغرافي الحالي
  static Future<Position?> _getCurrentPosition() async {
    try {
      // التحقق من الأذونات
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          AppLogger.warning('تم رفض إذن الموقع');
          return _lastKnownPosition;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        AppLogger.warning('تم رفض إذن الموقع نهائياً');
        return _lastKnownPosition;
      }

      // الحصول على الموقع الحالي
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: const Duration(seconds: 10),
      );

      _lastKnownPosition = position;
      return position;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الموقع: $e');
      return _lastKnownPosition;
    }
  }

  /// الحصول على الأحداث القادمة
  static List<IslamicEvent> getUpcomingEvents({int daysAhead = 30}) {
    final today = HijriCalendar.now();
    final upcomingEvents = <IslamicEvent>[];
    final allEvents = IslamicEventsFactory.getFixedEvents();

    for (int i = 0; i < daysAhead; i++) {
      final checkDate = HijriCalendar()
        ..hYear = today.hYear
        ..hMonth = today.hMonth
        ..hDay = today.hDay + i;

      // تصحيح التاريخ إذا تجاوز نهاية الشهر
      if (checkDate.hDay > 30) {
        checkDate.hMonth += 1;
        checkDate.hDay -= 30;
        if (checkDate.hMonth > 12) {
          checkDate.hYear += 1;
          checkDate.hMonth = 1;
        }
      }

      final eventsForDay = allEvents
          .where((event) => event.isOnDate(checkDate))
          .toList();
      
      upcomingEvents.addAll(eventsForDay);
    }

    return upcomingEvents;
  }

  /// تحويل الأرقام إلى العربية
  static String convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }

  /// الحصول على اسم الشهر الميلادي بالعربية
  static String getGregorianMonthName(int month) {
    const monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return monthNames[month - 1];
  }

  /// الحصول على اسم اليوم بالعربية
  static String getDayName(int weekday) {
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return dayNames[weekday - 1];
  }

  /// التحقق من كون التاريخ اليوم
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  /// التحقق من كون التاريخ في المستقبل
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// التحقق من كون التاريخ في الماضي
  static bool isPast(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final checkDate = DateTime(date.year, date.month, date.day);
    return checkDate.isBefore(today);
  }

  /// الحصول على لون الحدث حسب نوعه
  static int getEventColor(IslamicEventType type) {
    switch (type) {
      case IslamicEventType.eid:
        return 0xFF4CAF50; // أخضر للأعياد
      case IslamicEventType.religious:
        return 0xFF2196F3; // أزرق للمناسبات الدينية
      case IslamicEventType.historical:
        return 0xFF9C27B0; // بنفسجي للأحداث التاريخية
      case IslamicEventType.special:
        return 0xFFFF9800; // برتقالي للمناسبات الخاصة
    }
  }

  /// تنسيق التاريخ الهجري
  static String formatHijriDate(HijriCalendar hijri) {
    return '${convertToArabicNumbers(hijri.hDay)} ${_getHijriMonthName(hijri.hMonth)} ${convertToArabicNumbers(hijri.hYear)} هـ';
  }

  /// تنسيق التاريخ الميلادي
  static String formatGregorianDate(DateTime date) {
    return '${convertToArabicNumbers(date.day)} ${getGregorianMonthName(date.month)} ${convertToArabicNumbers(date.year)} م';
  }

  /// الحصول على اسم الشهر الهجري
  static String _getHijriMonthName(int month) {
    const monthNames = [
      'محرم',
      'صفر',
      'ربيع الأول',
      'ربيع الثاني',
      'جمادى الأولى',
      'جمادى الثانية',
      'رجب',
      'شعبان',
      'رمضان',
      'شوال',
      'ذو القعدة',
      'ذو الحجة',
    ];
    return monthNames[month - 1];
  }
}
