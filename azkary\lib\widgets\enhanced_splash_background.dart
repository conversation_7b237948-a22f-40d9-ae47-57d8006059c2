import 'package:flutter/material.dart';
import 'dart:math' as math;

/// خلفية محسنة لشاشة البداية مع تأثيرات بصرية جميلة
class EnhancedSplashBackground extends StatefulWidget {
  final Widget child;
  final bool isDarkMode;

  const EnhancedSplashBackground({
    super.key,
    required this.child,
    required this.isDarkMode,
  });

  @override
  State<EnhancedSplashBackground> createState() =>
      _EnhancedSplashBackgroundState();
}

class _EnhancedSplashBackgroundState extends State<EnhancedSplashBackground>
    with TickerProviderStateMixin {
  late AnimationController _particleController;
  late AnimationController _gradientController;
  late Animation<double> _particleAnimation;
  late Animation<double> _gradientAnimation;

  @override
  void initState() {
    super.initState();

    // تحكم في حركة الجسيمات
    _particleController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    // تحكم في تدرج الألوان
    _gradientController = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    )..repeat(reverse: true);

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );

    _gradientAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _gradientController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _particleController.dispose();
    _gradientController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // التدرج اللوني المتحرك
        AnimatedBuilder(
          animation: _gradientAnimation,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(gradient: _buildAnimatedGradient()),
            );
          },
        ),

        // الجسيمات المتحركة
        AnimatedBuilder(
          animation: _particleAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: ParticlesPainter(
                animation: _particleAnimation.value,
                isDarkMode: widget.isDarkMode,
              ),
              size: Size.infinite,
            );
          },
        ),

        // الزخارف الإسلامية المحسنة
        Positioned.fill(
          child: CustomPaint(
            painter: EnhancedIslamicPatternPainter(
              isDarkMode: widget.isDarkMode,
            ),
          ),
        ),

        // المحتوى الرئيسي
        widget.child,
      ],
    );
  }

  LinearGradient _buildAnimatedGradient() {
    final t = _gradientAnimation.value;

    if (widget.isDarkMode) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color.lerp(const Color(0xFF0D1B2A), const Color(0xFF1B263B), t)!,
          Color.lerp(const Color(0xFF1B263B), const Color(0xFF415A77), t)!,
          Color.lerp(const Color(0xFF415A77), const Color(0xFF0D1B2A), t)!,
        ],
        stops: [0.0, 0.5, 1.0],
      );
    } else {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color.lerp(const Color(0xFFF8F9FA), const Color(0xFFE9ECEF), t)!,
          Color.lerp(const Color(0xFFE9ECEF), const Color(0xFFDEE2E6), t)!,
          Color.lerp(const Color(0xFFDEE2E6), const Color(0xFFF8F9FA), t)!,
        ],
        stops: [0.0, 0.5, 1.0],
      );
    }
  }
}

/// رسام الجسيمات المتحركة
class ParticlesPainter extends CustomPainter {
  final double animation;
  final bool isDarkMode;
  final List<Particle> particles = [];

  ParticlesPainter({required this.animation, required this.isDarkMode}) {
    // إنشاء جسيمات عشوائية
    for (int i = 0; i < 20; i++) {
      particles.add(
        Particle(
          x: math.Random().nextDouble(),
          y: math.Random().nextDouble(),
          size: math.Random().nextDouble() * 3 + 1,
          speed: math.Random().nextDouble() * 0.5 + 0.2,
          opacity: math.Random().nextDouble() * 0.6 + 0.2,
        ),
      );
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      final x = (particle.x + animation * particle.speed) % 1.0 * size.width;
      final y = particle.y * size.height;

      paint.color =
          (isDarkMode
              ? Colors.white.withValues(alpha: particle.opacity * 0.3)
              : const Color(
                0xFF2E7D32,
              ).withValues(alpha: particle.opacity * 0.4));

      canvas.drawCircle(Offset(x, y), particle.size, paint);
    }
  }

  @override
  bool shouldRepaint(ParticlesPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

/// فئة الجسيمة
class Particle {
  final double x;
  final double y;
  final double size;
  final double speed;
  final double opacity;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speed,
    required this.opacity,
  });
}

/// رسام الزخارف الإسلامية المحسنة
class EnhancedIslamicPatternPainter extends CustomPainter {
  final bool isDarkMode;

  EnhancedIslamicPatternPainter({required this.isDarkMode});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;

    paint.color =
        isDarkMode
            ? Colors.white.withValues(alpha: 0.08)
            : const Color(0xFF2E7D32).withValues(alpha: 0.12);

    // رسم شبكة من الأشكال الهندسية الإسلامية
    final patternSize = 120.0;
    final cols = (size.width / patternSize).ceil() + 1;
    final rows = (size.height / patternSize).ceil() + 1;

    for (int i = 0; i < cols; i++) {
      for (int j = 0; j < rows; j++) {
        final centerX = i * patternSize - patternSize / 2;
        final centerY = j * patternSize - patternSize / 2;

        _drawGeometricPattern(
          canvas,
          paint,
          Offset(centerX, centerY),
          patternSize * 0.4,
        );
      }
    }
  }

  void _drawGeometricPattern(
    Canvas canvas,
    Paint paint,
    Offset center,
    double radius,
  ) {
    // رسم نجمة ثمانية محسنة
    final path = Path();
    const points = 8;

    for (int i = 0; i < points * 2; i++) {
      final angle = i * math.pi / points;
      final r = i.isEven ? radius : radius * 0.5;
      final x = center.dx + r * math.cos(angle);
      final y = center.dy + r * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    canvas.drawPath(path, paint);

    // دائرة داخلية
    canvas.drawCircle(center, radius * 0.3, paint);
  }

  @override
  bool shouldRepaint(EnhancedIslamicPatternPainter oldDelegate) {
    return oldDelegate.isDarkMode != isDarkMode;
  }
}
