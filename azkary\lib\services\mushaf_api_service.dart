import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/mushaf_page_model.dart';
import '../utils/logger.dart';

/// خدمة API للحصول على بيانات المصحف من مصادر خارجية
class MushafApiService {
  static const String _baseUrl = 'https://api.alquran.cloud/v1';
  static const String _uthmaniEdition = 'quran-uthmani'; // النص العثماني
  static const String _pageEdition = 'quran-uthmani-page'; // ترقيم الصفحات

  /// الحصول على بيانات السورة بالخط العثماني
  static Future<List<UthmaniAyah>> getSurahUthmani(int surahNumber) async {
    try {
      AppLogger.info('جاري تحميل السورة $surahNumber بالخط العثماني...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/surah/$surahNumber/editions/$_uthmaniEdition'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final ayahs = data['data'][0]['ayahs'] as List;
        
        final uthmaniAyahs = ayahs.map((ayah) => UthmaniAyah.fromJson(ayah)).toList();
        
        AppLogger.info('تم تحميل ${uthmaniAyahs.length} آية بالخط العثماني');
        return uthmaniAyahs;
      } else {
        throw Exception('فشل في تحميل السورة: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل السورة بالخط العثماني: $e');
      rethrow;
    }
  }

  /// الحصول على بيانات الصفحة من المصحف
  static Future<MushafPageData> getPageData(int pageNumber) async {
    try {
      AppLogger.info('جاري تحميل صفحة المصحف رقم $pageNumber...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/page/$pageNumber/editions/$_uthmaniEdition'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final ayahs = data['data']['ayahs'] as List;
        
        final pageData = MushafPageData.fromApiResponse(data['data'], ayahs);
        
        AppLogger.info('تم تحميل صفحة المصحف رقم $pageNumber');
        return pageData;
      } else {
        throw Exception('فشل في تحميل الصفحة: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل صفحة المصحف: $e');
      rethrow;
    }
  }

  /// الحصول على معلومات الجزء والحزب
  static Future<JuzHizbInfo> getJuzHizbInfo(int surahNumber, int ayahNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/ayah/$surahNumber:$ayahNumber'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return JuzHizbInfo.fromJson(data['data']);
      } else {
        throw Exception('فشل في تحميل معلومات الجزء والحزب');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل معلومات الجزء والحزب: $e');
      rethrow;
    }
  }

  /// الحصول على قائمة السور مع معلومات الصفحات
  static Future<List<SurahPageInfo>> getAllSurahsPageInfo() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/meta'),
        headers: {'Accept': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final surahs = data['data']['surahs']['references'] as List;
        
        return surahs.map((surah) => SurahPageInfo.fromJson(surah)).toList();
      } else {
        throw Exception('فشل في تحميل معلومات السور');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل معلومات السور: $e');
      rethrow;
    }
  }
}

/// نموذج للآية بالخط العثماني
class UthmaniAyah {
  final int number;
  final String text;
  final int numberInSurah;
  final int juz;
  final int page;
  final int hizbQuarter;
  final bool sajda;

  UthmaniAyah({
    required this.number,
    required this.text,
    required this.numberInSurah,
    required this.juz,
    required this.page,
    required this.hizbQuarter,
    required this.sajda,
  });

  factory UthmaniAyah.fromJson(Map<String, dynamic> json) {
    return UthmaniAyah(
      number: json['number'],
      text: json['text'],
      numberInSurah: json['numberInSurah'],
      juz: json['juz'],
      page: json['page'],
      hizbQuarter: json['hizbQuarter'],
      sajda: json['sajda'] ?? false,
    );
  }
}

/// نموذج لبيانات صفحة المصحف
class MushafPageData {
  final int pageNumber;
  final List<UthmaniAyah> ayahs;
  final List<int> surahs;
  final String edition;

  MushafPageData({
    required this.pageNumber,
    required this.ayahs,
    required this.surahs,
    required this.edition,
  });

  factory MushafPageData.fromApiResponse(Map<String, dynamic> data, List ayahsList) {
    final ayahs = ayahsList.map((ayah) => UthmaniAyah.fromJson(ayah)).toList();
    final surahs = ayahs.map((ayah) => ayah.numberInSurah).toSet().toList();
    
    return MushafPageData(
      pageNumber: data['number'],
      ayahs: ayahs,
      surahs: surahs,
      edition: data['edition']['identifier'],
    );
  }
}

/// نموذج لمعلومات الجزء والحزب
class JuzHizbInfo {
  final int juz;
  final int hizb;
  final int quarter;
  final int page;

  JuzHizbInfo({
    required this.juz,
    required this.hizb,
    required this.quarter,
    required this.page,
  });

  factory JuzHizbInfo.fromJson(Map<String, dynamic> json) {
    return JuzHizbInfo(
      juz: json['juz'],
      hizb: json['hizb'],
      quarter: json['hizbQuarter'],
      page: json['page'],
    );
  }
}

/// نموذج لمعلومات السورة والصفحات
class SurahPageInfo {
  final int number;
  final String name;
  final String englishName;
  final int numberOfAyahs;
  final String revelationType;

  SurahPageInfo({
    required this.number,
    required this.name,
    required this.englishName,
    required this.numberOfAyahs,
    required this.revelationType,
  });

  factory SurahPageInfo.fromJson(Map<String, dynamic> json) {
    return SurahPageInfo(
      number: json['number'],
      name: json['name'],
      englishName: json['englishName'],
      numberOfAyahs: json['numberOfAyahs'],
      revelationType: json['revelationType'],
    );
  }
}
