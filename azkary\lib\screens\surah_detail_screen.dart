import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart' as share_plus;

import '../models/quran_model.dart';
import '../models/ayah_view_mode.dart';
import '../services/quran_provider.dart';
import '../services/theme_provider.dart';
import '../widgets/islamic_background.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/shimmer_loading.dart';
import '../utils/logger.dart';

/// شاشة عرض تفاصيل السورة
class SurahDetailScreen extends StatefulWidget {
  final Surah surah;
  final int? initialAyahNumber; // رقم الآية المراد الانتقال إليها

  const SurahDetailScreen({
    super.key,
    required this.surah,
    this.initialAyahNumber,
  });

  @override
  State<SurahDetailScreen> createState() => _SurahDetailScreenState();
}

class _SurahDetailScreenState extends State<SurahDetailScreen>
    with TickerProviderStateMixin {
  List<Ayah> _ayahs = [];
  bool _isLoading = true;
  String _error = '';

  // تعريف متغير للتحكم في التمرير
  final ScrollController _scrollController = ScrollController();

  // متغيرات الوميض
  late AnimationController _highlightController;
  late Animation<double> _highlightAnimation;
  int? _highlightedAyahNumber;

  @override
  void initState() {
    super.initState();

    // تهيئة الوميض
    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _highlightAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _highlightController, curve: Curves.easeInOut),
    );

    _loadAyahs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _highlightController.dispose();
    super.dispose();
  }

  // تم إزالة دوال التنقل بين السور

  /// تحميل آيات السورة
  Future<void> _loadAyahs() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      AppLogger.info(
        'بدء تحميل آيات السورة: ${widget.surah.name} (${widget.surah.number})',
      );
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);

      // محاولة تحميل الآيات مع التفسير مباشرة
      try {
        final ayahsWithTafsir = await quranProvider.getAyahsWithTafsir(
          widget.surah.number,
        );

        if (ayahsWithTafsir.isNotEmpty) {
          AppLogger.info(
            'تم تحميل ${ayahsWithTafsir.length} آية مع التفسير من سورة ${widget.surah.name}',
          );

          // تسجيل معلومات عن أول آية وآخر آية للتشخيص
          if (ayahsWithTafsir.isNotEmpty) {
            final firstAyah = ayahsWithTafsir.first;
            final lastAyah = ayahsWithTafsir.last;

            AppLogger.info(
              'أول آية: رقم=${firstAyah.numberInSurah}, isBismillah=${firstAyah.isBismillah}, النص=${firstAyah.text.substring(0, firstAyah.text.length > 20 ? 20 : firstAyah.text.length)}...',
            );

            AppLogger.info(
              'آخر آية: رقم=${lastAyah.numberInSurah}, النص=${lastAyah.text.substring(0, lastAyah.text.length > 20 ? 20 : lastAyah.text.length)}...',
            );
          }

          if (mounted) {
            setState(() {
              _ayahs = ayahsWithTafsir;
              _isLoading = false;
            });

            // التمرير إلى الآية المحددة بعد تحميل الآيات
            if (widget.initialAyahNumber != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _scrollToAyah(widget.initialAyahNumber!);
              });
            }
          }

          return;
        }
      } catch (tafsirError) {
        AppLogger.warning(
          'خطأ في تحميل الآيات مع التفسير: $tafsirError، محاولة تحميل الآيات فقط',
        );
      }

      // إذا فشل تحميل الآيات مع التفسير، نحاول تحميل الآيات فقط
      final ayahs = await quranProvider.getAyahs(widget.surah.number);

      if (ayahs.isEmpty) {
        AppLogger.warning(
          'لم يتم تحميل أي آيات من السورة ${widget.surah.name}',
        );
        if (mounted) {
          setState(() {
            _error =
                'لم يتم تحميل أي آيات. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.';
            _isLoading = false;
          });
        }
        return;
      }

      AppLogger.info(
        'تم تحميل ${ayahs.length} آية من سورة ${widget.surah.name} (بدون تفسير)',
      );

      // تسجيل معلومات عن أول آية وآخر آية للتشخيص
      if (ayahs.isNotEmpty) {
        final firstAyah = ayahs.first;
        final lastAyah = ayahs.last;

        AppLogger.info(
          'أول آية (بدون تفسير): رقم=${firstAyah.numberInSurah}, isBismillah=${firstAyah.isBismillah}, النص=${firstAyah.text.substring(0, firstAyah.text.length > 20 ? 20 : firstAyah.text.length)}...',
        );

        AppLogger.info(
          'آخر آية (بدون تفسير): رقم=${lastAyah.numberInSurah}, النص=${lastAyah.text.substring(0, lastAyah.text.length > 20 ? 20 : lastAyah.text.length)}...',
        );
      }

      if (mounted) {
        setState(() {
          _ayahs = ayahs;
          _isLoading = false;
        });

        // التمرير إلى الآية المحددة بعد تحميل الآيات
        if (widget.initialAyahNumber != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToAyah(widget.initialAyahNumber!);
          });
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل آيات السورة: $e');
      if (mounted) {
        setState(() {
          _error = 'حدث خطأ أثناء تحميل الآيات: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: widget.surah.name,
        actions: [
          // زر تغيير وضع العرض
          PopupMenuButton<AyahViewMode>(
            tooltip: 'طريقة العرض',
            icon: Icon(
              Icons.view_list,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            onSelected: (AyahViewMode mode) {
              HapticFeedback.lightImpact();
              final themeProvider = Provider.of<ThemeProvider>(
                context,
                listen: false,
              );
              themeProvider.setAyahViewMode(mode);
            },
            itemBuilder: (BuildContext context) {
              final themeProvider = Provider.of<ThemeProvider>(
                context,
                listen: false,
              );
              return <PopupMenuEntry<AyahViewMode>>[
                PopupMenuItem<AyahViewMode>(
                  value: AyahViewMode.list,
                  child: Row(
                    children: [
                      Icon(
                        Icons.view_list,
                        color:
                            themeProvider.ayahViewMode == AyahViewMode.list
                                ? theme.colorScheme.primary
                                : null,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'قائمة',
                        style: TextStyle(
                          color:
                              themeProvider.ayahViewMode == AyahViewMode.list
                                  ? theme.colorScheme.primary
                                  : null,
                          fontWeight:
                              themeProvider.ayahViewMode == AyahViewMode.list
                                  ? FontWeight.bold
                                  : null,
                        ),
                      ),
                    ],
                  ),
                ),

                PopupMenuItem<AyahViewMode>(
                  value: AyahViewMode.tafsir,
                  child: Row(
                    children: [
                      Icon(
                        Icons.format_align_center,
                        color:
                            themeProvider.ayahViewMode == AyahViewMode.tafsir
                                ? theme.colorScheme.primary
                                : null,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'تفسير',
                        style: TextStyle(
                          color:
                              themeProvider.ayahViewMode == AyahViewMode.tafsir
                                  ? theme.colorScheme.primary
                                  : null,
                          fontWeight:
                              themeProvider.ayahViewMode == AyahViewMode.tafsir
                                  ? FontWeight.bold
                                  : null,
                        ),
                      ),
                    ],
                  ),
                ),

                PopupMenuItem<AyahViewMode>(
                  value: AyahViewMode.continuousScroll,
                  child: Row(
                    children: [
                      Icon(
                        Icons.vertical_align_center,
                        color:
                            themeProvider.ayahViewMode ==
                                    AyahViewMode.continuousScroll
                                ? theme.colorScheme.primary
                                : null,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'تمرير متواصل',
                        style: TextStyle(
                          color:
                              themeProvider.ayahViewMode ==
                                      AyahViewMode.continuousScroll
                                  ? theme.colorScheme.primary
                                  : null,
                          fontWeight:
                              themeProvider.ayahViewMode ==
                                      AyahViewMode.continuousScroll
                                  ? FontWeight.bold
                                  : null,
                        ),
                      ),
                    ],
                  ),
                ),
              ];
            },
          ),
          // تم إزالة زر التحديث
        ],
      ),
      body: IslamicBackground(
        opacity: 0.03,
        showPattern: true,
        showGradient: false,
        child: SafeArea(
          child: Column(
            children: [
              // قائمة الآيات
              Expanded(
                child:
                    _isLoading
                        ? _buildLoadingState()
                        : _error.isNotEmpty
                        ? _buildErrorState()
                        : _ayahs.isEmpty
                        ? _buildEmptyState()
                        : _buildAyahsList(),
              ),

              // تم إزالة أزرار التنقل بين السور
            ],
          ),
        ),
      ),
    );
  }

  // تم إزالة دالة _buildSurahInfo() لتوفير مساحة أكبر للعرض

  // تم إزالة الدوال غير المستخدمة

  // تم استبدال هذه الدالة بدالة _buildLineTextSpans

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final baseColor = isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300;

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ShimmerLoading(
            isLoading: true,
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: baseColor,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAyahs,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد آيات متاحة حالياً',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'سيتم توفير الآيات قريباً إن شاء الله',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAyahs,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  /// التمرير إلى آية محددة مع الوميض
  void _scrollToAyah(int ayahNumber) {
    // التأكد من أن الآية موجودة في القائمة
    if (_ayahs.isEmpty || ayahNumber <= 0 || ayahNumber > _ayahs.length) {
      AppLogger.warning('لا يمكن التمرير إلى الآية $ayahNumber: خارج النطاق');
      return;
    }

    // تعيين الآية المراد إبرازها
    setState(() {
      _highlightedAyahNumber = ayahNumber;
    });

    // الحصول على مؤشر الآية في القائمة (الآيات تبدأ من 1 لكن المؤشر يبدأ من 0)
    final index = ayahNumber - 1;

    // الحصول على وضع العرض الحالي قبل التأخير
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final currentViewMode = themeProvider.ayahViewMode;

    AppLogger.info(
      'التمرير إلى الآية رقم $ayahNumber (index=$index) في وضع العرض: ${currentViewMode.arabicName}',
    );

    // بدء الوميض
    _highlightController.reset();
    _highlightController.forward();

    // تأخير قصير للتأكد من أن القائمة جاهزة للتمرير
    Future.delayed(const Duration(milliseconds: 300), () {
      // التحقق من أن الشاشة لا تزال مثبتة
      if (!mounted) return;

      // التمرير إلى الآية المحددة
      switch (currentViewMode) {
        case AyahViewMode.list:
          // في وضع القائمة، نستخدم التمرير العادي
          AppLogger.info('التمرير في وضع القائمة إلى الموقع: ${index * 150.0}');
          _scrollController.animateTo(
            index *
                150.0, // زيادة متوسط ارتفاع بطاقة الآية لضمان التمرير الصحيح
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
          break;

        case AyahViewMode.tafsir:
          // في وضع التفسير
          AppLogger.info('التمرير في وضع التفسير إلى الموقع: ${index * 200.0}');
          _scrollController.animateTo(
            index * 200.0, // زيادة متوسط ارتفاع بطاقة التفسير
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
          break;

        case AyahViewMode.continuousScroll:
          // في وضع التمرير المتواصل
          // نحتاج إلى حساب موقع الآية بشكل تقريبي
          // نفترض أن كل آية تأخذ حوالي 100 بكسل + علامات الأجزاء والأحزاب
          AppLogger.info(
            'التمرير في وضع التمرير المتواصل إلى الموقع: ${index * 100.0 + 200}',
          );
          _scrollController.animateTo(
            index * 100.0 + 200, // إضافة 200 للتعويض عن رأس السورة والبسملة
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
          break;
      }

      // إيقاف الوميض بعد 3 ثوانٍ
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _highlightedAyahNumber = null;
          });
          _highlightController.reset();
        }
      });
    });
  }

  /// الحصول على الآيات المفلترة للعرض
  List<Ayah> _getFilteredAyahs() {
    // الآيات تأتي معالجة من QuranService، لذا نعيدها مباشرة
    return _ayahs;
  }

  /// بناء قائمة الآيات
  Widget _buildAyahsList() {
    final themeProvider = Provider.of<ThemeProvider>(context);

    // عرض الآيات حسب وضع العرض المحدد
    switch (themeProvider.ayahViewMode) {
      case AyahViewMode.list:
        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: _getFilteredAyahs().length,
          itemBuilder: (context, index) {
            final ayah = _getFilteredAyahs()[index];
            return _buildAyahCard(ayah);
          },
        );

      case AyahViewMode.tafsir:
        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: _getFilteredAyahs().length,
          itemBuilder: (context, index) {
            final ayah = _getFilteredAyahs()[index];
            return _buildTafsirCard(ayah);
          },
        );

      case AyahViewMode.continuousScroll:
        return _buildContinuousScrollView();
    }
  }

  /// بناء عرض التمرير المتواصل للآيات
  Widget _buildContinuousScrollView() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return ListView(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      children: [
        // معلومات السورة
        _buildSurahHeader(),

        const SizedBox(height: 24),

        // عرض الآيات بشكل متواصل
        ..._buildContinuousAyahs(isDarkMode, theme),

        const SizedBox(height: 24),

        // نهاية السورة
        _buildSurahFooter(),
      ],
    );
  }

  /// بناء رأس السورة للعرض المتواصل
  Widget _buildSurahHeader() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: isDarkMode ? const Color(0xFF1E2732) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // اسم السورة
            Text(
              widget.surah.name,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // معلومات السورة
            Text(
              '${widget.surah.numberOfAyahs} آية - ${widget.surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية'}',
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode ? Colors.white70 : Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الآيات بشكل متواصل
  List<Widget> _buildContinuousAyahs(bool isDarkMode, ThemeData theme) {
    List<Widget> ayahWidgets = [];
    int currentJuz = 0;
    int currentHizbQuarter = 0;
    List<InlineSpan> currentParagraphSpans = [];

    // إضافة البسملة في بداية السورة إذا لم تكن سورة التوبة
    if (widget.surah.number != 9) {
      // إضافة البسملة كعنوان مميز
      ayahWidgets.add(
        Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 24),
          child: Text(
            'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
            style: TextStyle(
              fontSize: 26,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
              height: 2.0,
              fontFamily: 'Uthmani',
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    // تحديد نمط النص الأساسي
    final TextStyle baseTextStyle = TextStyle(
      fontSize: 22,
      height: 2.0,
      letterSpacing: 0.5,
      fontFamily: 'Uthmani',
      color: isDarkMode ? Colors.white : Colors.black87,
    );

    // إضافة الآيات
    for (int i = 0; i < _ayahs.length; i++) {
      final ayah = _ayahs[i];

      // في سورة الفاتحة، البسملة هي الآية الأولى
      if (ayah.isBismillah && widget.surah.number == 1) {
        // نعرض البسملة كآية في سورة الفاتحة فقط
        // سيتم التعامل معها في نهاية الحلقة
      }

      // إضافة علامة الجزء إذا تغير
      if (ayah.juz != currentJuz) {
        // إضافة الفقرة الحالية قبل علامة الجزء إذا كانت غير فارغة
        if (currentParagraphSpans.isNotEmpty) {
          ayahWidgets.add(_buildContinuousParagraph(currentParagraphSpans));
          currentParagraphSpans = [];
        }

        currentJuz = ayah.juz;
        ayahWidgets.add(_buildJuzMarker(currentJuz));
      }

      // إضافة علامة الحزب إذا تغير
      if (ayah.hizbQuarter != currentHizbQuarter) {
        // إضافة الفقرة الحالية قبل علامة الحزب إذا كانت غير فارغة
        if (currentParagraphSpans.isNotEmpty) {
          ayahWidgets.add(_buildContinuousParagraph(currentParagraphSpans));
          currentParagraphSpans = [];
        }

        currentHizbQuarter = ayah.hizbQuarter;

        // إذا كان ربع حزب جديد (وليس بداية جزء)
        if (currentHizbQuarter % 4 != 1 || ayah.juz != currentJuz) {
          ayahWidgets.add(_buildHizbQuarterMarker(currentHizbQuarter));
        }
      }

      // إضافة نص الآية إلى الفقرة الحالية
      String ayahText = ayah.text;

      // إضافة النص فقط إذا كان غير فارغ
      if (ayahText.isNotEmpty) {
        // إزالة جميع النقاط السوداء والعلامات المزعجة من النص القرآني
        ayahText = _cleanArabicText(ayahText);

        currentParagraphSpans.add(
          TextSpan(text: '$ayahText ', style: baseTextStyle),
        );
      }

      // إضافة رقم الآية
      currentParagraphSpans.add(
        WidgetSpan(
          alignment: PlaceholderAlignment.middle,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              borderRadius: BorderRadius.circular(50),
              border: Border.all(
                color: theme.colorScheme.primary.withAlpha(50),
                width: 1,
              ),
            ),
            child: Text(
              '${ayah.numberInSurah}',
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    // إضافة الفقرة الأخيرة إذا كانت غير فارغة
    if (currentParagraphSpans.isNotEmpty) {
      ayahWidgets.add(_buildContinuousParagraph(currentParagraphSpans));
    }

    return ayahWidgets;
  }

  /// تنظيف النص العربي من النقاط السوداء والعلامات المزعجة
  String _cleanArabicText(String text) {
    // إزالة جميع أنواع النقاط السوداء والعلامات المزعجة
    String cleanedText = text;

    // إزالة النقطة السوداء بعد ألف الجماعة (U+06DF)
    cleanedText = cleanedText.replaceAll('وا۟', 'وا');
    cleanedText = cleanedText.replaceAll('وا۟ ', 'وا ');

    // إزالة النقطة السوداء المنفردة (U+06DF)
    cleanedText = cleanedText.replaceAll('۟', '');

    // إزالة علامة الوقف الصغيرة (U+06E0)
    cleanedText = cleanedText.replaceAll('۠', '');

    // إزالة علامة الوقف المدورة (U+06E1)
    cleanedText = cleanedText.replaceAll('ۡ', '');

    // إزالة علامة الوقف المربعة (U+06E2)
    cleanedText = cleanedText.replaceAll('ۢ', '');

    // إزالة علامة الوقف المثلثة (U+06E3)
    cleanedText = cleanedText.replaceAll('ۣ', '');

    // إزالة علامة الوقف المدببة (U+06E4)
    cleanedText = cleanedText.replaceAll('ۤ', '');

    // إزالة علامة الوقف الطويلة (U+06E5)
    cleanedText = cleanedText.replaceAll('ۥ', '');

    // إزالة علامة الوقف القصيرة (U+06E6)
    cleanedText = cleanedText.replaceAll('ۦ', '');

    // إزالة علامة الوقف المنحنية (U+06E7)
    cleanedText = cleanedText.replaceAll('ۧ', '');

    // إزالة علامة الوقف المقلوبة (U+06E8)
    cleanedText = cleanedText.replaceAll('ۨ', '');

    // إزالة أي نقاط سوداء أخرى قد تظهر
    cleanedText = cleanedText.replaceAll('ۭ', ''); // U+06ED
    cleanedText = cleanedText.replaceAll('ۮ', ''); // U+06EE
    cleanedText = cleanedText.replaceAll('ۯ', ''); // U+06EF

    // إزالة المسافات الزائدة
    cleanedText = cleanedText.replaceAll(RegExp(r'\s+'), ' ').trim();

    return cleanedText;
  }

  /// بناء فقرة متواصلة من الآيات
  Widget _buildContinuousParagraph(List<InlineSpan> spans) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: RichText(
        textAlign: TextAlign.justify,
        textDirection: TextDirection.rtl,
        text: TextSpan(children: spans),
      ),
    );
  }

  /// بناء علامة الجزء
  Widget _buildJuzMarker(int juzNumber) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Divider(
              color: theme.colorScheme.primary.withAlpha(100),
              thickness: 1,
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(30),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: theme.colorScheme.primary.withAlpha(100),
                width: 1,
              ),
            ),
            child: Text(
              'الجزء $juzNumber',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
          Expanded(
            child: Divider(
              color: theme.colorScheme.primary.withAlpha(100),
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء علامة ربع الحزب
  Widget _buildHizbQuarterMarker(int hizbQuarter) {
    final theme = Theme.of(context);
    final int hizb = ((hizbQuarter - 1) ~/ 4) + 1;
    final int quarter = ((hizbQuarter - 1) % 4) + 1;
    String quarterText;

    switch (quarter) {
      case 1:
        quarterText = 'الحزب $hizb';
        break;
      case 2:
        quarterText = 'ربع الحزب $hizb';
        break;
      case 3:
        quarterText = 'نصف الحزب $hizb';
        break;
      case 4:
        quarterText = 'ثلاثة أرباع الحزب $hizb';
        break;
      default:
        quarterText = 'ربع $hizbQuarter';
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: Divider(color: Colors.grey.withAlpha(150), thickness: 0.5),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 12),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(30),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              quarterText,
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withAlpha(180),
              ),
            ),
          ),
          Expanded(
            child: Divider(color: Colors.grey.withAlpha(150), thickness: 0.5),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل السورة
  Widget _buildSurahFooter() {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      child: Text(
        'صدق الله العظيم',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
          fontStyle: FontStyle.italic,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء بطاقة الآية مع دعم التمييز البصري
  Widget _buildAyahCard(Ayah ayah) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final isHighlighted = _highlightedAyahNumber == ayah.numberInSurah;

    // إذا كانت البسملة، نعرضها بشكل مميز فقط في سورة الفاتحة
    if (ayah.isBismillah) {
      // في سورة الفاتحة، البسملة هي الآية الأولى
      final bool isFatiha = widget.surah.number == 1;

      return AnimatedBuilder(
        animation: _highlightAnimation,
        builder: (context, child) {
          return Card(
            elevation: isHighlighted ? 8 : 2,
            margin: const EdgeInsets.only(bottom: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color:
                isHighlighted
                    ? theme.colorScheme.primary.withAlpha(30)
                    : (isDarkMode ? const Color(0xFF1E2732) : Colors.white),
            child: Container(
              decoration:
                  isHighlighted
                      ? BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.colorScheme.primary.withAlpha(
                            (255 * _highlightAnimation.value).round(),
                          ),
                          width: 2,
                        ),
                      )
                      : null,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Center(
                      child: Text(
                        _cleanArabicText(ayah.text),
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color:
                              isHighlighted
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.primary,
                          height: 2.0,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // إذا كانت البسملة في سورة الفاتحة، نعرض رقم الآية
                    if (isFatiha) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withAlpha(26),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          'الآية ${ayah.numberInSurah}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      );
    }

    // الآيات العادية مع دعم التمييز البصري

    return AnimatedBuilder(
      animation: _highlightAnimation,
      builder: (context, child) {
        return Card(
          elevation: isHighlighted ? 4 : 2,
          margin: const EdgeInsets.only(bottom: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          color:
              isHighlighted
                  ? Color.lerp(
                    isDarkMode ? const Color(0xFF1E2732) : Colors.white,
                    theme.colorScheme.primary.withAlpha(50),
                    _highlightAnimation.value * 0.5,
                  )
                  : (isDarkMode ? const Color(0xFF1E2732) : Colors.white),
          child: Container(
            decoration:
                isHighlighted
                    ? BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.colorScheme.primary.withAlpha(
                          (100 + (_highlightAnimation.value * 155)).round(),
                        ),
                        width: 1 + (_highlightAnimation.value * 2),
                      ),
                    )
                    : null,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // رقم الآية
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withAlpha(
                            26,
                          ), // 0.1 * 255 = ~26
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: theme.colorScheme.primary.withAlpha(
                              77,
                            ), // 0.3 * 255 = ~77
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            '${ayah.numberInSurah}',
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                      const Spacer(),
                      // أزرار الإجراءات
                      IconButton(
                        icon: Icon(
                          Icons.copy,
                          size: 20,
                          color: theme.colorScheme.primary,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: ayah.text));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('تم نسخ الآية'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                        tooltip: 'نسخ',
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.share,
                          size: 20,
                          color: theme.colorScheme.primary,
                        ),
                        onPressed: () {
                          // مشاركة الآية
                          final surahName = widget.surah.name;
                          final ayahNumber = ayah.numberInSurah;
                          final shareText =
                              'سورة $surahName - الآية $ayahNumber:\n${ayah.text}\n\nتطبيق أذكاري';

                          // استخدام SharePlus بالطريقة الصحيحة
                          final box = context.findRenderObject() as RenderBox?;
                          share_plus.SharePlus.instance.share(
                            share_plus.ShareParams(
                              text: shareText,
                              sharePositionOrigin:
                                  box != null
                                      ? box.localToGlobal(Offset.zero) &
                                          box.size
                                      : null,
                            ),
                          );
                        },
                        tooltip: 'مشاركة',
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // نص الآية
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 4,
                    ),
                    child: Text(
                      _cleanArabicText(ayah.text),
                      style: TextStyle(
                        fontSize: 22,
                        height: 1.8,
                        letterSpacing: 0.5,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                  if (ayah.sajda) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber.withAlpha(51), // 0.2 * 255 = ~51
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.volunteer_activism,
                            size: 16,
                            color: Colors.amber.shade800,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'سجدة',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.amber.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // تم إزالة دالة _buildTafsirView لأنها لم تعد مستخدمة

  /// بناء بطاقة التفسير
  Widget _buildTafsirCard(Ayah ayah) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final bool isFatiha = widget.surah.number == 1;

    // إذا كانت البسملة، نعرضها بشكل مميز فقط في سورة الفاتحة
    if (ayah.isBismillah) {
      return Card(
        elevation: 2,
        margin: const EdgeInsets.only(bottom: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: isDarkMode ? const Color(0xFF1E2732) : Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Center(
                child: Text(
                  _cleanArabicText(ayah.text),
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                    height: 2.0,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // إذا كانت البسملة في سورة الفاتحة، نعرض رقم الآية
              if (isFatiha) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withAlpha(26),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'الآية ${ayah.numberInSurah}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: isDarkMode ? const Color(0xFF1E2732) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رقم الآية
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withAlpha(26),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                'الآية ${ayah.numberInSurah}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // نص الآية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withAlpha(10),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.primary.withAlpha(30),
                  width: 1,
                ),
              ),
              child: Text(
                _cleanArabicText(ayah.text),
                style: TextStyle(
                  fontSize: 20,
                  height: 1.8,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
                textDirection: TextDirection.rtl,
              ),
            ),
            const SizedBox(height: 16),

            // عنوان التفسير
            Row(
              children: [
                Icon(
                  Icons.format_quote,
                  size: 18,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'التفسير الميسر',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // نص التفسير
            Text(
              ayah.tafsir ??
                  'تفسير الآية ${ayah.numberInSurah} من سورة ${widget.surah.name} سيظهر هنا عند توفر التفاسير.',
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color: isDarkMode ? Colors.white70 : Colors.black87,
              ),
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
            ),

            const SizedBox(height: 16),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.copy,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: ayah.text));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم نسخ الآية'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  tooltip: 'نسخ',
                ),
                IconButton(
                  icon: Icon(
                    Icons.share,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () {
                    // مشاركة الآية
                    final surahName = widget.surah.name;
                    final ayahNumber = ayah.numberInSurah;
                    final shareText =
                        'سورة $surahName - الآية $ayahNumber:\n${ayah.text}\n\nتطبيق أذكاري';

                    // استخدام SharePlus بالطريقة الصحيحة
                    final box = context.findRenderObject() as RenderBox?;
                    share_plus.SharePlus.instance.share(
                      share_plus.ShareParams(
                        text: shareText,
                        sharePositionOrigin:
                            box != null
                                ? box.localToGlobal(Offset.zero) & box.size
                                : null,
                      ),
                    );
                  },
                  tooltip: 'مشاركة',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // تم إزالة دالة _buildNavigationButtons() لتوفير مساحة أكبر للعرض
}
