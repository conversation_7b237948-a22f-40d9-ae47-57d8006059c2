/// نموذج بيانات لصفحة المصحف
class MushafPage {
  final int pageNumber;
  final String surahName;
  final List<MushafLine> lines;
  final bool hasBasmala;
  final int? juzNumber;
  final int? hizbNumber;

  MushafPage({
    required this.pageNumber,
    required this.surahName,
    required this.lines,
    this.hasBasmala = false,
    this.juzNumber,
    this.hizbNumber,
  });
}

/// نموذج بيانات لسطر في صفحة المصحف
class MushafLine {
  final String text;
  final List<AyahInLine> ayahs;
  final bool isBasmala;

  MushafLine({required this.text, required this.ayahs, this.isBasmala = false});
}

/// نموذج بيانات للآية داخل السطر
class AyahInLine {
  final int ayahNumber;
  final int surahNumber;
  final String ayahText;
  final int startIndex;
  final int endIndex;
  final bool hasAyahNumber;

  AyahInLine({
    required this.ayahNumber,
    required this.surahNumber,
    required this.ayahText,
    required this.startIndex,
    required this.endIndex,
    this.hasAyahNumber = true,
  });
}

/// بيانات تخطيط المصحف - تحتوي على معلومات كيفية توزيع الآيات على الصفحات
class MushafLayoutData {
  /// خريطة تربط رقم الصفحة بالآيات الموجودة فيها
  static const Map<int, List<Map<String, dynamic>>> pageToAyahs = {
    // صفحة 1 - سورة الفاتحة
    1: [
      {
        'surah': 1,
        'ayahStart': 1,
        'ayahEnd': 7,
        'lines': [
          {
            'text': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            'ayahs': [
              {'ayah': 1, 'start': 0, 'end': -1},
            ],
          },
          {
            'text': 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ ﴿٢﴾',
            'ayahs': [
              {'ayah': 2, 'start': 0, 'end': -1},
            ],
          },
          {
            'text': 'الرَّحْمَٰنِ الرَّحِيمِ ﴿٣﴾',
            'ayahs': [
              {'ayah': 3, 'start': 0, 'end': -1},
            ],
          },
          {
            'text': 'مَالِكِ يَوْمِ الدِّينِ ﴿٤﴾',
            'ayahs': [
              {'ayah': 4, 'start': 0, 'end': -1},
            ],
          },
          {
            'text': 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ ﴿٥﴾',
            'ayahs': [
              {'ayah': 5, 'start': 0, 'end': -1},
            ],
          },
          {
            'text': 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ ﴿٦﴾',
            'ayahs': [
              {'ayah': 6, 'start': 0, 'end': -1},
            ],
          },
          {
            'text':
                'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ ﴿٧﴾',
            'ayahs': [
              {'ayah': 7, 'start': 0, 'end': -1},
            ],
          },
        ],
      },
    ],

    // صفحة 2 - بداية سورة البقرة
    2: [
      {
        'surah': 2,
        'ayahStart': 1,
        'ayahEnd': 5,
        'lines': [
          {'text': 'سُورَةُ الْبَقَرَةِ', 'ayahs': [], 'isSurahHeader': true},
          {
            'text': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
            'ayahs': [],
            'isBasmala': true,
          },
          {
            'text': 'الم ﴿١﴾',
            'ayahs': [
              {'ayah': 1, 'start': 0, 'end': -1},
            ],
          },
          {
            'text':
                'ذَٰلِكَ الْكِتَابُ لَا رَيْبَ ۛ فِيهِ ۛ هُدًى لِّلْمُتَّقِينَ ﴿٢﴾',
            'ayahs': [
              {'ayah': 2, 'start': 0, 'end': -1},
            ],
          },
          {
            'text':
                'الَّذِينَ يُؤْمِنُونَ بِالْغَيْبِ وَيُقِيمُونَ الصَّلَاةَ وَمِمَّا رَزَقْنَاهُمْ يُنفِقُونَ ﴿٣﴾',
            'ayahs': [
              {'ayah': 3, 'start': 0, 'end': -1},
            ],
          },
          {
            'text':
                'وَالَّذِينَ يُؤْمِنُونَ بِمَا أُنزِلَ إِلَيْكَ وَمَا أُنزِلَ مِن قَبْلِكَ وَبِالْآخِرَةِ هُمْ يُوقِنُونَ ﴿٤﴾',
            'ayahs': [
              {'ayah': 4, 'start': 0, 'end': -1},
            ],
          },
          {
            'text':
                'أُولَٰئِكَ عَلَىٰ هُدًى مِّن رَّبِّهِمْ ۖ وَأُولَٰئِكَ هُمُ الْمُفْلِحُونَ ﴿٥﴾',
            'ayahs': [
              {'ayah': 5, 'start': 0, 'end': -1},
            ],
          },
        ],
      },
    ],
  };

  /// الحصول على بيانات صفحة معينة
  static MushafPage? getPageData(int pageNumber) {
    final pageData = pageToAyahs[pageNumber];
    if (pageData == null) return null;

    final surahData = pageData.first;
    final lines = <MushafLine>[];

    for (final lineData in surahData['lines'] as List<Map<String, dynamic>>) {
      final ayahsInLine = <AyahInLine>[];

      for (final ayahData in lineData['ayahs'] as List<Map<String, dynamic>>) {
        ayahsInLine.add(
          AyahInLine(
            ayahNumber: ayahData['ayah'],
            surahNumber: surahData['surah'],
            ayahText: lineData['text'],
            startIndex: ayahData['start'],
            endIndex: ayahData['end'],
          ),
        );
      }

      lines.add(
        MushafLine(
          text: lineData['text'],
          ayahs: ayahsInLine,
          isBasmala: lineData['isBasmala'] ?? false,
        ),
      );
    }

    return MushafPage(
      pageNumber: pageNumber,
      surahName: _getSurahName(surahData['surah']),
      lines: lines,
      hasBasmala: lines.any((line) => line.isBasmala),
    );
  }

  /// الحصول على اسم السورة
  static String _getSurahName(int surahNumber) {
    switch (surahNumber) {
      case 1:
        return 'الفاتحة';
      case 2:
        return 'البقرة';
      case 3:
        return 'آل عمران';
      case 4:
        return 'النساء';
      case 5:
        return 'المائدة';
      default:
        return 'سورة $surahNumber';
    }
  }

  /// الحصول على رقم الصفحة للآية المحددة
  static int? getPageForAyah(int surahNumber, int ayahNumber) {
    for (final entry in pageToAyahs.entries) {
      final pageNumber = entry.key;
      final pageData = entry.value;

      for (final surahData in pageData) {
        if (surahData['surah'] == surahNumber) {
          final ayahStart = surahData['ayahStart'] as int;
          final ayahEnd = surahData['ayahEnd'] as int;

          if (ayahNumber >= ayahStart && ayahNumber <= ayahEnd) {
            return pageNumber;
          }
        }
      }
    }
    return null;
  }
}
