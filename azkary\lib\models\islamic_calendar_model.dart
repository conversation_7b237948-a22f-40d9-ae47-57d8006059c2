import 'package:hijri/hijri.dart';

/// نموذج بيانات التقويم الإسلامي
class IslamicCalendarModel {
  final HijriCalendar hijriDate;
  final DateTime gregorianDate;
  final List<IslamicEvent> events;
  final MoonPhase moonPhase;
  final PrayerTimes? prayerTimes;

  IslamicCalendarModel({
    required this.hijriDate,
    required this.gregorianDate,
    required this.events,
    required this.moonPhase,
    this.prayerTimes,
  });

  /// الحصول على اسم الشهر الهجري
  String get hijriMonthName {
    const monthNames = [
      'محرم',
      'صفر',
      'ربيع الأول',
      'ربيع الثاني',
      'جمادى الأولى',
      'جمادى الثانية',
      'رجب',
      'شعبان',
      'رمضان',
      'شوال',
      'ذو القعدة',
      'ذو الحجة',
    ];
    return monthNames[hijriDate.hMonth - 1];
  }

  /// الحصول على اسم اليوم
  String get dayName {
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return dayNames[gregorianDate.weekday - 1];
  }

  /// التحقق من كون اليوم جمعة
  bool get isFriday => gregorianDate.weekday == DateTime.friday;

  /// التحقق من وجود أحداث في هذا اليوم
  bool get hasEvents => events.isNotEmpty;
}

/// نموذج الأحداث الإسلامية
class IslamicEvent {
  final String name;
  final String description;
  final IslamicEventType type;
  final int hijriDay;
  final int hijriMonth;

  IslamicEvent({
    required this.name,
    required this.description,
    required this.type,
    required this.hijriDay,
    required this.hijriMonth,
  });

  /// التحقق من كون الحدث في تاريخ معين
  bool isOnDate(HijriCalendar date) {
    return date.hDay == hijriDay && date.hMonth == hijriMonth;
  }
}

/// أنواع الأحداث الإسلامية
enum IslamicEventType {
  eid, // عيد
  religious, // مناسبة دينية
  historical, // حدث تاريخي
  special, // مناسبة خاصة
}

/// مراحل القمر
enum MoonPhase {
  newMoon, // هلال جديد
  waxingCrescent, // هلال متزايد
  firstQuarter, // ربع أول
  waxingGibbous, // أحدب متزايد
  fullMoon, // بدر
  waningGibbous, // أحدب متناقص
  lastQuarter, // ربع أخير
  waningCrescent, // هلال متناقص
}

/// أوقات الصلاة
class PrayerTimes {
  final DateTime fajr;
  final DateTime sunrise;
  final DateTime dhuhr;
  final DateTime asr;
  final DateTime maghrib;
  final DateTime isha;

  PrayerTimes({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.asr,
    required this.maghrib,
    required this.isha,
  });

  /// تحويل الوقت إلى نص
  String formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// الحصول على قائمة أوقات الصلاة
  List<PrayerTimeItem> get prayerList => [
    PrayerTimeItem('الفجر', fajr),
    PrayerTimeItem('الشروق', sunrise),
    PrayerTimeItem('الظهر', dhuhr),
    PrayerTimeItem('العصر', asr),
    PrayerTimeItem('المغرب', maghrib),
    PrayerTimeItem('العشاء', isha),
  ];
}

/// عنصر وقت الصلاة
class PrayerTimeItem {
  final String name;
  final DateTime time;

  PrayerTimeItem(this.name, this.time);
}

/// مصنع الأحداث الإسلامية
class IslamicEventsFactory {
  /// الحصول على الأحداث الإسلامية الثابتة
  static List<IslamicEvent> getFixedEvents() {
    return [
      // شهر محرم
      IslamicEvent(
        name: 'رأس السنة الهجرية',
        description: 'بداية السنة الهجرية الجديدة',
        type: IslamicEventType.religious,
        hijriDay: 1,
        hijriMonth: 1,
      ),
      IslamicEvent(
        name: 'يوم عاشوراء',
        description: 'اليوم العاشر من محرم',
        type: IslamicEventType.religious,
        hijriDay: 10,
        hijriMonth: 1,
      ),

      // شهر ربيع الأول
      IslamicEvent(
        name: 'المولد النبوي الشريف',
        description: 'ذكرى مولد النبي محمد صلى الله عليه وسلم',
        type: IslamicEventType.religious,
        hijriDay: 12,
        hijriMonth: 3,
      ),

      // شهر رجب
      IslamicEvent(
        name: 'الإسراء والمعراج',
        description: 'ذكرى رحلة الإسراء والمعراج',
        type: IslamicEventType.religious,
        hijriDay: 27,
        hijriMonth: 7,
      ),

      // شهر شعبان
      IslamicEvent(
        name: 'ليلة النصف من شعبان',
        description: 'ليلة البراءة',
        type: IslamicEventType.religious,
        hijriDay: 15,
        hijriMonth: 8,
      ),

      // شهر رمضان
      IslamicEvent(
        name: 'بداية شهر رمضان',
        description: 'أول أيام الصيام',
        type: IslamicEventType.religious,
        hijriDay: 1,
        hijriMonth: 9,
      ),
      IslamicEvent(
        name: 'ليلة القدر',
        description: 'ليلة خير من ألف شهر',
        type: IslamicEventType.religious,
        hijriDay: 27,
        hijriMonth: 9,
      ),

      // شهر شوال
      IslamicEvent(
        name: 'عيد الفطر',
        description: 'عيد انتهاء شهر رمضان',
        type: IslamicEventType.eid,
        hijriDay: 1,
        hijriMonth: 10,
      ),

      // شهر ذو الحجة
      IslamicEvent(
        name: 'يوم عرفة',
        description: 'اليوم التاسع من ذي الحجة',
        type: IslamicEventType.religious,
        hijriDay: 9,
        hijriMonth: 12,
      ),
      IslamicEvent(
        name: 'عيد الأضحى',
        description: 'عيد الأضحى المبارك',
        type: IslamicEventType.eid,
        hijriDay: 10,
        hijriMonth: 12,
      ),
    ];
  }

  /// الحصول على الأحداث لتاريخ معين
  static List<IslamicEvent> getEventsForDate(HijriCalendar date) {
    return getFixedEvents()
        .where((event) => event.isOnDate(date))
        .toList();
  }
}

/// حاسبة مراحل القمر
class MoonPhaseCalculator {
  /// حساب مرحلة القمر لتاريخ معين
  static MoonPhase calculateMoonPhase(DateTime date) {
    // حساب مبسط لمراحل القمر بناءً على التاريخ الهجري
    final hijri = HijriCalendar.fromDate(date);
    final dayOfMonth = hijri.hDay;

    if (dayOfMonth <= 2) {
      return MoonPhase.newMoon;
    } else if (dayOfMonth <= 6) {
      return MoonPhase.waxingCrescent;
    } else if (dayOfMonth <= 9) {
      return MoonPhase.firstQuarter;
    } else if (dayOfMonth <= 13) {
      return MoonPhase.waxingGibbous;
    } else if (dayOfMonth <= 16) {
      return MoonPhase.fullMoon;
    } else if (dayOfMonth <= 20) {
      return MoonPhase.waningGibbous;
    } else if (dayOfMonth <= 24) {
      return MoonPhase.lastQuarter;
    } else {
      return MoonPhase.waningCrescent;
    }
  }

  /// الحصول على أيقونة مرحلة القمر
  static String getMoonPhaseIcon(MoonPhase phase) {
    switch (phase) {
      case MoonPhase.newMoon:
        return '🌑';
      case MoonPhase.waxingCrescent:
        return '🌒';
      case MoonPhase.firstQuarter:
        return '🌓';
      case MoonPhase.waxingGibbous:
        return '🌔';
      case MoonPhase.fullMoon:
        return '🌕';
      case MoonPhase.waningGibbous:
        return '🌖';
      case MoonPhase.lastQuarter:
        return '🌗';
      case MoonPhase.waningCrescent:
        return '🌘';
    }
  }

  /// الحصول على اسم مرحلة القمر
  static String getMoonPhaseName(MoonPhase phase) {
    switch (phase) {
      case MoonPhase.newMoon:
        return 'هلال جديد';
      case MoonPhase.waxingCrescent:
        return 'هلال متزايد';
      case MoonPhase.firstQuarter:
        return 'ربع أول';
      case MoonPhase.waxingGibbous:
        return 'أحدب متزايد';
      case MoonPhase.fullMoon:
        return 'بدر';
      case MoonPhase.waningGibbous:
        return 'أحدب متناقص';
      case MoonPhase.lastQuarter:
        return 'ربع أخير';
      case MoonPhase.waningCrescent:
        return 'هلال متناقص';
    }
  }
}
