import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import '../models/mushaf_page_model.dart';
import '../services/mushaf_api_service.dart';

/// ويدجت لعرض صفحة مصحف المدينة النبوية بالتخطيط الأصلي
class MadinahMushafWidget extends StatelessWidget {
  final MushafPage page;
  final int? highlightedAyahNumber;
  final Function(int ayahNumber)? onAyahTap;

  const MadinahMushafWidget({
    Key? key,
    required this.page,
    this.highlightedAyahNumber,
    this.onAyahTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        // خلفية مصحف المدينة النبوية
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDarkMode
              ? [
                  const Color(0xFF2C1810),
                  const Color(0xFF1A0F08),
                ]
              : [
                  const Color(0xFFFFFDF5), // كريمي فاتح
                  const Color(0xFFF8F6E8), // كريمي أغمق قليلاً
                ],
        ),
        border: Border.all(
          color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس الصفحة مع الزخارف
          _buildPageHeader(theme, isDarkMode),
          
          // محتوى الصفحة
          Expanded(
            child: _buildPageContent(theme, isDarkMode),
          ),
          
          // تذييل الصفحة مع رقم الصفحة
          _buildPageFooter(theme, isDarkMode),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة مع الزخارف الإسلامية
  Widget _buildPageHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            (isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513))
                .withValues(alpha: 0.1),
            Colors.transparent,
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            width: 2,
          ),
        ),
      ),
      child: Column(
        children: [
          // زخرفة إسلامية علوية
          _buildIslamicDecoration(isDarkMode),
          
          const SizedBox(height: 12),
          
          // معلومات الصفحة (الجزء، الحزب، السورة)
          _buildPageInfo(theme, isDarkMode),
          
          const SizedBox(height: 12),
          
          // زخرفة إسلامية سفلية
          _buildIslamicDecoration(isDarkMode),
        ],
      ),
    );
  }

  /// بناء الزخرفة الإسلامية
  Widget _buildIslamicDecoration(bool isDarkMode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildDecorationElement(isDarkMode),
        const SizedBox(width: 20),
        _buildCenterDecoration(isDarkMode),
        const SizedBox(width: 20),
        _buildDecorationElement(isDarkMode),
      ],
    );
  }

  /// عنصر زخرفي جانبي
  Widget _buildDecorationElement(bool isDarkMode) {
    return Container(
      width: 40,
      height: 3,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  /// الزخرفة المركزية
  Widget _buildCenterDecoration(bool isDarkMode) {
    return Container(
      width: 60,
      height: 20,
      decoration: BoxDecoration(
        border: Border.all(
          color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  /// بناء معلومات الصفحة
  Widget _buildPageInfo(ThemeData theme, bool isDarkMode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // معلومات الجزء والحزب
        if (page.juzNumber != null)
          Text(
            'الجزء ${_convertToArabicNumbers(page.juzNumber!)}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
              fontFamily: 'Amiri',
            ),
          ),
        
        // اسم السورة
        Text(
          page.surahName,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            fontFamily: 'Amiri',
          ),
        ),
        
        // رقم الصفحة
        Text(
          _convertToArabicNumbers(page.pageNumber),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            fontFamily: 'Amiri',
          ),
        ),
      ],
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildPageContent(ThemeData theme, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // محتوى الصفحة القابل للتمرير
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: page.lines
                    .map((line) => _buildMushafLine(line, theme, isDarkMode))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء سطر المصحف بالخط العثماني
  Widget _buildMushafLine(MushafLine line, ThemeData theme, bool isDarkMode) {
    if (line.isBasmala) {
      return _buildBasmalaLine(line, theme, isDarkMode);
    }

    // التحقق من كون السطر رأس سورة
    if (line.ayahs.isEmpty && (line.text.contains('سورة') || line.text.contains('سُورَة'))) {
      return _buildSurahHeaderLine(line, theme, isDarkMode);
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: RichText(
        textAlign: TextAlign.justify,
        textDirection: TextDirection.rtl,
        text: TextSpan(
          children: _buildLineSpans(line, theme, isDarkMode),
        ),
      ),
    );
  }

  /// بناء سطر البسملة بتصميم مصحف المدينة
  Widget _buildBasmalaLine(MushafLine line, ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 24),
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            (isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513))
                .withValues(alpha: 0.05),
            Colors.transparent,
          ],
        ),
        border: Border.symmetric(
          horizontal: BorderSide(
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            width: 2,
          ),
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // زخرفة علوية للبسملة
          _buildBasmalaDecoration(isDarkMode),
          
          const SizedBox(height: 16),
          
          // نص البسملة بالخط العثماني
          Text(
            line.text,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
              fontFamily: 'Uthmani', // خط عثماني
              height: 2.0,
              letterSpacing: 1.5,
            ),
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),
          
          const SizedBox(height: 16),
          
          // زخرفة سفلية للبسملة
          _buildBasmalaDecoration(isDarkMode),
        ],
      ),
    );
  }

  /// زخرفة البسملة
  Widget _buildBasmalaDecoration(bool isDarkMode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(5, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: index == 2 ? 12 : 8,
          height: index == 2 ? 12 : 8,
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            shape: BoxShape.circle,
          ),
        );
      }),
    );
  }

  /// بناء رأس السورة
  Widget _buildSurahHeaderLine(MushafLine line, ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 20),
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            (isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513))
                .withValues(alpha: 0.1),
            Colors.transparent,
          ],
        ),
        border: Border.symmetric(
          horizontal: BorderSide(
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            width: 3,
          ),
        ),
      ),
      child: Text(
        line.text,
        style: TextStyle(
          fontSize: 26,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
          fontFamily: 'Amiri',
          height: 1.5,
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  /// بناء أجزاء النص في السطر
  List<TextSpan> _buildLineSpans(MushafLine line, ThemeData theme, bool isDarkMode) {
    final spans = <TextSpan>[];
    final text = line.text;
    int currentIndex = 0;

    for (final ayah in line.ayahs) {
      // إضافة النص قبل الآية إذا وجد
      if (ayah.startIndex > currentIndex) {
        spans.add(TextSpan(
          text: text.substring(currentIndex, ayah.startIndex),
          style: _getUthmaniTextStyle(isDarkMode, false),
        ));
      }

      // إضافة نص الآية
      final ayahEndIndex = ayah.endIndex == -1 ? text.length : ayah.endIndex;
      final ayahText = text.substring(ayah.startIndex, ayahEndIndex);
      final isHighlighted = highlightedAyahNumber == ayah.ayahNumber;

      spans.add(TextSpan(
        text: ayahText,
        style: _getUthmaniTextStyle(isDarkMode, isHighlighted),
        recognizer: onAyahTap != null
            ? (TapGestureRecognizer()..onTap = () => onAyahTap!(ayah.ayahNumber))
            : null,
      ));

      currentIndex = ayahEndIndex;
    }

    // إضافة النص المتبقي إذا وجد
    if (currentIndex < text.length) {
      spans.add(TextSpan(
        text: text.substring(currentIndex),
        style: _getUthmaniTextStyle(isDarkMode, false),
      ));
    }

    return spans;
  }

  /// الحصول على نمط النص العثماني
  TextStyle _getUthmaniTextStyle(bool isDarkMode, bool isHighlighted) {
    return TextStyle(
      fontSize: 22,
      height: 2.5, // تباعد أكبر للخط العثماني
      fontWeight: FontWeight.w500,
      color: isHighlighted
          ? (isDarkMode ? const Color(0xFFFFD700) : const Color(0xFF8B0000))
          : (isDarkMode ? const Color(0xFFF5F5DC) : const Color(0xFF2F4F4F)),
      fontFamily: 'Uthmani', // خط عثماني
      letterSpacing: 1.0,
      backgroundColor: isHighlighted
          ? (isDarkMode
              ? const Color(0xFFD4AF37).withValues(alpha: 0.3)
              : const Color(0xFF8B4513).withValues(alpha: 0.2))
          : null,
    );
  }

  /// بناء تذييل الصفحة
  Widget _buildPageFooter(ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: isDarkMode ? const Color(0xFFD4AF37) : const Color(0xFF8B4513),
            width: 2,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildIslamicDecoration(isDarkMode),
        ],
      ),
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
